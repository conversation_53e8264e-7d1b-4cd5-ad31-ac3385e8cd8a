/**
 * TUTOR LMS DASHBOARD ÖZEL CSS DOSYASI
 * Bu dosya Tutor LMS eklentisinin dashboard görünümünü özelleştirmek için kullanılır.
 *
 * İÇİNDEKİLER:
 * 1. Genel Dashboard Stilleri
 * 2. <PERSON><PERSON>
 * 3. <PERSON><PERSON> Stilleri
 * 4. <PERSON><PERSON><PERSON><PERSON>
 * 5. Responsive (Mobil) Stilleri
 * 6. Eklenen CSS Dosyaları
 *    - Avatar Stilleri
 *    - Avatar Dropdown Menü
 *    - <PERSON>zel Kaydırma <PERSON>ubuğu
 *    - Buton <PERSON>zelt<PERSON>eri
 *    - Başlık Animasyonları
 *    - Görünürlük Düzeltmeleri
 *    - vb.
 */

/*--------------------------------------------------------------
1. GENEL DASHBOARD STİLLERİ
--------------------------------------------------------------*/

/* Dashboard için temel stiller */
body.tutor-dashboard-page {
    margin: 0 !important;
    padding: 0 !important;
    overflow-x: hidden !important;
}

/* Dashboard sidebar ve header için border rengini kapat */
body.tutor-dashboard-page .tutor-dashboard-left-menu,
body.tutor-dashboard-page .tutor-frontend-dashboard-header {
    --tutor-border-color: transparent !important;
}

/* Tema header'ını gizle */
body.tutor-dashboard-page #masthead,
body.tutor-dashboard-page header.site-header,
body.tutor-dashboard-page header.wp-block-template-part,
body.tutor-dashboard-page header.entry-header,
body.tutor-dashboard-page .site-header,
body.tutor-dashboard-page .main-header,
body.tutor-dashboard-page .header-area,
body.tutor-dashboard-page .elementor-location-header,
body.tutor-dashboard-page #header,
body.tutor-dashboard-page .header:not(.tutor-dashboard-header):not(.tutor-frontend-dashboard-header),
body.tutor-dashboard-page nav.main-navigation,
body.tutor-dashboard-page nav.primary-navigation,
body.tutor-dashboard-page .navigation-top,
body.tutor-dashboard-page .top-header,
body.tutor-dashboard-page .top-bar {
    display: none !important;
}

/* Tema sidebar'ını gizle */
body.tutor-dashboard-page .site-sidebar,
body.tutor-dashboard-page .sidebar,
body.tutor-dashboard-page .widget-area,
body.tutor-dashboard-page aside.sidebar {
    display: none !important;
}

/* Ana içerik alanını tam genişliğe ayarla - sadece 991px ve üzeri ekranlar için */
body.tutor-dashboard-page .site-content,
body.tutor-dashboard-page #content,
body.tutor-dashboard-page .content-area,
body.tutor-dashboard-page main.site-main,
body.tutor-dashboard-page .tutor-container,
body.tutor-dashboard-page .tutor-wrap,
body.tutor-dashboard-page .tutor-dashboard-content-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
}
    
/*--------------------------------------------------------------
2. HEADER STİLLERİ
--------------------------------------------------------------*/

/* Dashboard header alanını düzenle */
body.tutor-dashboard-page .tutor-dashboard-header,
body.tutor-dashboard-page .tutor-frontend-dashboard-header,
body.tutor-dashboard-page .tutor-header-left-side,
body.tutor-dashboard-page .tutor-header-right-side {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Header içindeki sol ve sağ tarafı düzenle */
body.tutor-dashboard-page .tutor-header-left-side {
    flex: 1 !important; /* Sol tarafı genişlet */
}

body.tutor-dashboard-page .tutor-header-right-side {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-end !important; /* Sağa hizala */
    flex-direction: row !important; /* Buton solda, profil sağda olacak */
}

/* Avatar ve profil alanı düzenlemeleri */
body.tutor-dashboard-page .tutor-dashboard-header-avatar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-left: 15px !important;
    order: 2 !important; /* Avatar sonra gelsin */
}

body.tutor-dashboard-page .tutor-user-info {
    display: flex !important;
    flex-direction: column !important;
    visibility: visible !important;
    opacity: 1 !important;
    order: 3 !important; /* Kullanıcı bilgileri en sonda gelsin */
}

/* Avatar boyutunu ayarla ve ortala */
body.tutor-dashboard-page .tutor-avatar-xl,
body.tutor-dashboard-page .tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
    width: 35px !important;
    height: 35px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Avatar içindeki resmi ortala */
body.tutor-dashboard-page .tutor-dashboard-header-avatar .tutor-avatar img {
    margin: 0 auto !important;
    display: block !important;
}

/* Create a New Course butonunu düzenle */
body.tutor-dashboard-page .tutor-header-right-side .tutor-create-new-course,
body.tutor-dashboard-page .tutor-header-right-side a.tutor-btn {
    margin-right: 15px !important;
    order: 1 !important; /* Buton önce gelsin */
}

/* Profil bölümünü sağ taraftaki header alanına ekle */
body.tutor-dashboard-page .tutor-header-right-side:before {
    content: "" !important;
    display: block !important;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>') !important;
    width: 24px !important;
    height: 24px !important;
    margin-right: 10px !important;
    visibility: hidden !important; /* Görünmez yap, sadece yer tutucu olarak kullan */
}

/* Profil bilgilerini düzenle */
body.tutor-dashboard-page .tutor-dashboard-header-username,
body.tutor-dashboard-page .tutor-dashboard-header-stats,
body.tutor-dashboard-page .tutor-dashboard-header-ratings,
body.tutor-dashboard-page .tutor-dashboard-header-display-name,
body.tutor-dashboard-page .tutor-dashboard-header-greetings {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Profil isminin boyutunu küçült */
body.tutor-dashboard-page .tutor-dashboard-header-username,
body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info .tutor-fs-4 {
    font-size: 14px !important; /* Daha küçük font boyutu */
    line-height: 1.2 !important; /* Satır yüksekliğini azalt */
}

/*--------------------------------------------------------------
3. SIDEBAR STİLLERİ
--------------------------------------------------------------*/

/* Sidebar'ı Sticky Yap ve Genişliğini Ayarla */
body.tutor-dashboard-page .tutor-dashboard-left-menu {
    position: fixed !important;
    height: 100vh !important;
    overflow-y: auto !important;
    z-index: 100 !important;
    width: 320px !important; /* Sabit genişlik */
    border-right: 1px solid #e9ecef !important; /* Sağ kenarlık ekle */
    background-color: #ffffff !important; /* Gündüz modunda beyaz arka plan */

    /* Kaydırma çubuğu stilini ayarla - başlangıçta şeffaf olsun */
    scrollbar-width: thin !important; /* Firefox için ince kaydırma çubuğu */
    scrollbar-color: transparent transparent !important; /* Firefox için başlangıçta şeffaf kaydırma çubuğu */
    -ms-overflow-style: auto !important; /* IE ve Edge için */
}

/* Sidebar kaydırma çubuğu stilleri */
body.tutor-dashboard-page .tutor-dashboard-left-menu::-webkit-scrollbar {
    width: 5px !important; /* Kaydırma çubuğu genişliği */
    background-color: transparent !important; /* Kaydırma çubuğu arka plan rengi */
    display: block !important; /* Her zaman göster */
}

body.tutor-dashboard-page .tutor-dashboard-left-menu::-webkit-scrollbar-thumb {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0) !important; /* Başlangıçta şeffaf kaydırma çubuğu */
    border-radius: 10px !important; /* Kaydırma çubuğu köşe yuvarlaklığı */
    transition: background-color 0.2s ease !important; /* Geçiş efekti */
}

body.tutor-dashboard-page .tutor-dashboard-left-menu:hover::-webkit-scrollbar-thumb {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important; /* Hover durumunda görünür kaydırma çubuğu */
}

body.tutor-dashboard-page .tutor-dashboard-left-menu::-webkit-scrollbar-thumb:hover {
    background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 1) !important; /* Kaydırma çubuğu hover durumunda tam opaklık */
}

body.tutor-dashboard-page .tutor-dashboard-left-menu:hover {
    scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important; /* Firefox için görünür kaydırma çubuğu */
}

/* Logo kısmını gizle */
body.tutor-dashboard-page .tutor-dashboard-logo-container {
    display: none !important;
}

/* Sidebar'da avatar alanı */
body.tutor-dashboard-page .tutor-sidebar-avatar-container {
    padding: 20px !important;
    text-align: center !important;
    border-bottom: 1px solid #e9ecef !important;
    margin-bottom: 10px !important;
}

body.tutor-dashboard-page .tutor-sidebar-avatar-container .tutor-avatar {
    width: 120px !important;
    height: 120px !important;
    margin: 0 auto 15px auto !important;
    display: block !important;
}

/* Sidebar'daki kullanıcı bilgileri */
body.tutor-dashboard-page .tutor-sidebar-user-info {
    text-align: center !important;
    margin-top: 10px !important;
}

body.tutor-dashboard-page .tutor-sidebar-user-name {
    font-size: 16px !important;
    font-weight: 600 !important;
    color: var(--tutor-color-black) !important;
    margin-bottom: 5px !important;
    line-height: 1.2 !important;
}

body.tutor-dashboard-page .tutor-sidebar-user-email {
    font-size: 14px !important;
    color: var(--tutor-color-muted) !important;
    margin-bottom: 0 !important;
    line-height: 1.2 !important;
    opacity: 0.8 !important;
}

/* Dark mode için kullanıcı bilgileri */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-sidebar-user-name,
body.tutor-dark-mode.tutor-dashboard-page .tutor-sidebar-user-name {
    color: #ffffff !important;
}

html[data-theme="dark"] body.tutor-dashboard-page .tutor-sidebar-user-email,
body.tutor-dark-mode.tutor-dashboard-page .tutor-sidebar-user-email {
    color: #b0b0b0 !important;
}

/* Dark mode için sidebar avatar alanı ve border */
html[data-theme="dark"] body.tutor-dashboard-page .tutor-sidebar-avatar-container,
body.tutor-dark-mode.tutor-dashboard-page .tutor-sidebar-avatar-container {
    border-bottom-color: #2c3046 !important;
}

html[data-theme="dark"] body.tutor-dashboard-page .tutor-dashboard-left-menu,
body.tutor-dark-mode.tutor-dashboard-page .tutor-dashboard-left-menu {
    border-right-color: #2c3046 !important;
}

/* Sidebar menü öğeleri */
body.tutor-dashboard-page .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-permalinks {
    padding: 10px 0px 0px 10px !important;
    margin-right: 0 !important;
}

body.tutor-dashboard-page .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-menu-item-link {
    border-radius: 10px !important;
}

/* Header'daki avatar ve kullanıcı bilgilerini tamamen gizle */
body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info.tutor-ml-24,
body.tutor-dashboard-page .tutor-dashboard-header-avatar,
body.tutor-dashboard-page .tutor-avatar-dropdown {
    display: none !important;
}

/* Logo link padding ayarı */
#tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-frontend-dashboard-maincontent > div.tutor-col-12.tutor-col-md-4.tutor-col-lg-3.tutor-dashboard-left-menu > div > a {
    padding: 25px 0px 20px 0px !important;
    display: block !important;
}


/* Tüm aktif menü öğeleri için stil */
.tutor-dashboard-menu-item-link.is-active,
.tutor-dashboard-menu-item-link.active,
.tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link {
  background-color: color-mix(in srgb, var(--tutor-color-primary) 20%, transparent) !important;
  color: var(--tutor-color-primary) !important;
  font-weight: 600 !important;
  border-left: 3px solid var(--tutor-color-primary) !important;
}


/* Tüm aktif menü öğelerinin ikonları için stil */
 .tutor-dashboard-menu-item-link.is-active i,
 .tutor-dashboard-menu-item-link.active i,
 .tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link i,
 .tutor-dashboard-menu-item-link.is-active .tutor-dashboard-menu-item-icon,
 .tutor-dashboard-menu-item-link.active .tutor-dashboard-menu-item-icon,
 .tutor-dashboard-menu-item.active .tutor-dashboard-menu-item-link .tutor-dashboard-menu-item-icon {
   color: var(--tutor-color-primary) !important;
}
 

/*--------------------------------------------------------------
4. İÇERİK ALANI STİLLERİ
--------------------------------------------------------------*/

/* Ana İçerik Alanını Sidebar'a Göre Ayarla */
body.tutor-dashboard-page .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 {
    margin-left: 300px !important; /* Sidebar genişliği + ekstra boşluk */
    padding-left: 50px !important; /* İçeriği daha fazla sağa taşı */
    padding-top: 61px !important; /* Header yüksekliği + ekstra boşluk */
}

/* Dashboard içeriğini düzenle */
body.tutor-dashboard-page .tutor-dashboard-content {
    padding-left: 30px !important;
    padding-right: 30px !important;
    padding-top: 35px !important; /* Üst boşluğu 35px yap */
    margin-bottom: 100px !important; /* Alt boşluğu 100px yap */
    background-color: #ffffff !important; /* Gündüz modunda beyaz arka plan */
}

/* Dark Mod - Dashboard İçerik Alanı */
body.tutor-dark-mode.tutor-dashboard-page .tutor-dashboard-content,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-dashboard-content {
    background-color: #0f0f0f !important;
}

/* Dashboard başlıklarını düzenle */
body.tutor-dashboard-page .tutor-dashboard-title,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24.tutor-dashboard-title,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-16.tutor-text-capitalize,
body.tutor-dashboard-page .tutor-profile-title,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black,
body.tutor-dashboard-page .header-title.tutor-fs-5.tutor-fw-medium.tutor-color-black,
body.tutor-dashboard-page h3.tutor-fs-5,
body.tutor-dashboard-page h2.tutor-fs-5,
body.tutor-dashboard-page h1.tutor-fs-5 {
    display: block !important;
    font-size: 1.5rem !important;
    /* margin-bottom: 1.5rem !important; - Kaldırıldı */
    animation: none !important;
    transform: none !important;
    transition: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Dark Mod - Başlık Renkleri */
body.tutor-dark-mode.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24,
body.tutor-dark-mode.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black,
body.tutor-dark-mode.tutor-dashboard-page .tutor-dashboard-title,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-dashboard-title,
body.tutor-dark-mode.tutor-dashboard-page .tutor-color-black,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-color-black {
    color: #ffffff !important;
}


/* İçerik elemanlarının kenar boşluklarını düzenle */
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-col-lg-4,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-col-lg-3,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-col-md-6 {
    padding-left: 15px !important;
    padding-right: 15px !important;
}

/* Kurs Listesini Sağa Taşı */
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-dashboard-content-inner-table {
    margin-left: 30px !important;
}

/* Sticky Header Ayarları */
body.tutor-dashboard-page .tutor-frontend-dashboard-header,
body.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
    margin-right: 0 !important;
    position: fixed !important;
    top: 0 !important;
    z-index: 99 !important;
    background-color: #ffffff !important;
    padding: 15px 10px 15px 0px !important;
    border-bottom: none !important;
    width: calc(100% - 310px) !important; /* Tam genişlik - sidebar genişliği */
    left: 320px !important; /* Sidebar genişliği */
    display: flex !important;
    justify-content: flex-end !important; /* Öğeleri sağa hizala */
}

/* Dark Mod - Header Ayarları */
body.tutor-dark-mode.tutor-dashboard-page .tutor-frontend-dashboard-header,
body.tutor-dark-mode.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-frontend-dashboard-header,
html[data-theme="dark"] body.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
    background-color: #0f0f0f !important;
    border-bottom: none !important;
}

/* Sol taraftaki header alanını gizle */
body.tutor-dashboard-page .tutor-header-left-side.tutor-dashboard-header.tutor-col-md-6.tutor-d-flex.tutor-align-center {
    display: none !important;
}

/* Admin bar'ı olan sayfalar için üst boşluk ayarla */
body.admin-bar.tutor-dashboard-page {
    margin-top: 32px !important; /* Admin bar yüksekliği */
}

/* Admin bar varsa header'ın konumunu ayarla */
body.admin-bar.tutor-dashboard-page .tutor-frontend-dashboard-header,
body.admin-bar.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
    top: 32px !important; /* Admin bar yüksekliği */
}

/*--------------------------------------------------------------
5. RESPONSIVE (MOBİL) STİLLERİ
--------------------------------------------------------------*/

@media (max-width: 991px) {
    /* Mobil görünümde içerik alanını düzenle */
    body.tutor-dashboard-page .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 {
        margin-left: 0 !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
        width: 100% !important;
        padding-top: 61px !important; /* Mobil header yüksekliği + ekstra boşluk */
        max-width: 100% !important; /* 991px ve altı için max-width'i sıfırla */
    }

    /* 991px ve altı için içerik alanının sağ sınırını sıfırla */
    #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-frontend-dashboard-maincontent > div.tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div {
        max-width: 100% !important;
        padding-right: 15px !important;
        box-sizing: border-box !important;
    }


    /* Mobil görünümde sidebar kaydırma çubuğu stilleri */
    body.tutor-dashboard-page .tutor-dashboard-left-menu.show::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0) !important;
        transition: background-color 0.2s ease !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-left-menu.show:hover::-webkit-scrollbar-thumb {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-left-menu.show::-webkit-scrollbar-thumb:hover {
        background-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 1) !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-left-menu.show:hover {
        scrollbar-color: rgba(var(--tutor-primary-rgb, 67, 97, 238), 0.3) transparent !important;
    }

    /* Mobil görünümde logo ve sidebar stilleri */
    body.tutor-dashboard-page .tutor-dashboard-logo-container {
        border-bottom: none !important;
        margin-bottom: 0 !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-logo {
        width: 180px !important;
        height: 80px !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-logo img {
        max-width: 100% !important;
        max-height: 100% !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-left-menu.show {
        left: 0 !important;
    }

    /* Mobil görünümde içerik alanı stilleri */
    body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-dashboard-content-inner-table,
    body.tutor-dashboard-page .tutor-frontend-dashboard-header {
        margin-left: 15px !important;
        margin-right: 15px !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-content {
        padding-left: 15px !important;
        padding-right: 15px !important;
        margin-bottom: 100px !important; /* Alt boşluğu 100px yap */
    }

    /* Mobil görünümde başlık stilleri */
    body.tutor-dashboard-page .tutor-dashboard-content-inner h3,
    body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24,
    body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5,
    body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-dashboard-title,
    body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black,
    body.tutor-dashboard-page .header-title.tutor-fs-5.tutor-fw-medium.tutor-color-black,
    body.tutor-dashboard-page h3.tutor-fs-5,
    body.tutor-dashboard-page h2.tutor-fs-5,
    body.tutor-dashboard-page h1.tutor-fs-5 {
        display: block !important;
        font-size: 1.3rem !important;
        /* margin-bottom: 1.2rem !important; - Kaldırıldı */
        animation: none !important;
        transform: none !important;
        transition: none !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* Mobil görünümde header stilleri */
    body.tutor-dashboard-page .tutor-frontend-dashboard-header,
    body.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
        margin-left: 0 !important;
        padding: 15px 10px 15px 0px !important;
        border-bottom: none !important;
        width: 100% !important;
        left: 0 !important;
        position: fixed !important;
        justify-content: flex-end !important;
        background-color: #ffffff !important;
    }

    /* Mobil görünümde dark mod header stilleri */
    body.tutor-dark-mode.tutor-dashboard-page .tutor-frontend-dashboard-header,
    body.tutor-dark-mode.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header,
    html[data-theme="dark"] body.tutor-dashboard-page .tutor-frontend-dashboard-header,
    html[data-theme="dark"] body.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
        background-color: #0f0f0f !important;
        border-bottom: none !important;
    }

    /* iPhone ve küçük mobil cihazlarda header içindeki butonlar için özel ayarlar */
    @media (max-width: 767px) {
        body.tutor-dashboard-page .tutor-header-right-side {
            padding-right: 10px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-end !important;
        }

        /* Dark mod butonu için daha fazla alan */
        body.tutor-dashboard-page .tutor-theme-mode-toggle {
            margin-right: 10px !important;
        }
    }

    body.tutor-dashboard-page .tutor-header-left-side.tutor-dashboard-header.tutor-col-md-6.tutor-d-flex.tutor-align-center {
        display: none !important;
    }

    body.tutor-dashboard-page .tutor-header-right-side {
        display: flex !important;
        flex-direction: row !important;
        align-items: center !important;
        justify-content: flex-end !important;
        margin-top: 0px !important;
        margin-bottom: 0px !important;
    }

    /* Mobil görünümde buton ve profil stilleri */
    body.tutor-dashboard-page .tutor-header-right-side .tutor-create-new-course,
    body.tutor-dashboard-page .tutor-header-right-side a.tutor-btn {
        margin-right: 10px !important;
        font-size: 12px !important;
        order: 1 !important;
    }

    body.tutor-dashboard-page .tutor-header-right-side .tutor-dashboard-header-avatar {
        margin-left: 10px !important;
        order: 2 !important;
    }

    body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info {
        order: 3 !important;
    }

    body.tutor-dashboard-page .tutor-dashboard-header-username,
    body.tutor-dashboard-page .tutor-header-right-side .tutor-user-info .tutor-fs-4 {
        font-size: 12px !important;
        line-height: 1.1 !important;
    }

    body.tutor-dashboard-page .tutor-avatar-xl,
    body.tutor-dashboard-page .tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
        width: 35px !important;
        height: 35px !important;
    }

    /* Admin bar varsa mobil görünümde header'ın konumunu ayarla */
    body.admin-bar.tutor-dashboard-page .tutor-frontend-dashboard-header,
    body.admin-bar.tutor-dashboard-page #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header {
        top: 46px !important; /* Mobil admin bar yüksekliği */
    }
}

/* Mobil için admin bar ayarı */
@media (max-width: 782px) {
    body.admin-bar.tutor-dashboard-page {
        margin-top: 46px !important; /* Mobil admin bar yüksekliği */
    }
}

/* Masaüstü için içerik alanı genişliği - 1920px - 992px arası responsive */
@media (min-width: 992px) and (max-width: 1920px) {
    /* İçerik alanının genişliğini responsive hale getir */
    .tutor-col-lg-9 {
        width: calc(100% - 320px) !important; /* Sidebar genişliği + ekstra boşluk çıkarılarak hesapla - 1920px için biraz daha geniş */
        max-width: calc(100% - 320px) !important;
        transition: width 0.3s ease, max-width 0.3s ease !important;
    }

    /* İçerik alanının sağ sınırını avatarın en sağına denk gelecek şekilde ayarla */
    #tutor-page-wrap > div > div.tutor-container > div.tutor-row.tutor-frontend-dashboard-maincontent > div.tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div {
        max-width: 100% !important;
        padding-right: 30px !important; /* Sağ tarafta biraz boşluk bırak */
        box-sizing: border-box !important;
    }

    /* Header sağ tarafını düzenle */
    body.tutor-dashboard-page .tutor-header-right-side {
        padding-right: 30px !important;
    }
}

/* Büyük ekranlar için içerik alanı genişliği */
@media (min-width: 1921px) {
    .tutor-col-lg-9 {
        width: 85% !important; /* 1920px ve üzeri için biraz daha geniş */
    }
}

/* Küçük mobil cihazlar için özel ayarlar */
@media screen and (max-width: 767.98px) {
    .tutor-header-right-side {
        margin-top: 0px !important;
        margin-bottom: 0px !important;
    }
}

/*--------------------------------------------------------------
6. EKLENEN CSS DOSYALARI - AVATAR STİLLERİ
--------------------------------------------------------------*/

/**
 * Avatar Stilleri
 * Bu dosya, dashboard'daki avatar görünümünü düzenlemek için kullanılır.
 */

/* Avatar boyutunu ayarla ve ortala */
.tutor-dashboard-header-avatar .tutor-avatar.tutor-avatar-xl {
    width: 40px !important;
    height: 40px !important;
}

/* Avatar içindeki resmi ortala */
.tutor-dashboard-header-avatar .tutor-avatar img {
    margin: 0 auto !important;
    display: block !important;
}

/* Header ve Avatar stilleri */
.tutor-frontend-dashboard-header {
    background-color: #ffffff !important;
    padding: 15px 10px 15px 0px !important;
    border-bottom: none !important;
    display: flex !important;
}

/* Dark Mod - Header Stilleri */
body.tutor-dark-mode .tutor-frontend-dashboard-header,
html[data-theme="dark"] .tutor-frontend-dashboard-header {
    background-color: #0f0f0f !important;
    border-bottom: none !important;
}

/* Mobil görünüm için stiller */
@media (max-width: 991px) {
    .tutor-frontend-dashboard-header {
        width: 100% !important;
    }
}

/*--------------------------------------------------------------
7. EKLENEN CSS DOSYALARI - AVATAR DROPDOWN MENÜ
--------------------------------------------------------------*/

/**
 * Avatar Dropdown Menü Stilleri
 * Bu dosya, avatar üzerine tıklandığında açılan dropdown menünün stillerini içerir.
 */

/* Avatar'ı tıklanabilir hale getir ve imleç stilini değiştir */
.tutor-dashboard-header-avatar {
    cursor: pointer !important;
    position: relative !important;
}

/* Avatar dropdown menü konteyneri */
.tutor-avatar-dropdown {
    position: absolute !important;
    top: calc(100% + 10px) !important;
    right: 0 !important;
    background-color: #fff !important;
    border-radius: var(--tutor-radius, 8px) !important;
    box-shadow: var(--tutor-shadow-lg, 0 5px 15px rgba(0, 0, 0, 0.1)) !important;
    width: 220px !important;
    z-index: 99999 !important; /* Daha yüksek z-index değeri */
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateY(10px) !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important; /* Dropdown kapalıyken tıklanabilirliği engelle */
    border: 1px solid var(--tutor-gray-light, #e9ecef) !important;
    overflow: hidden !important;
}

/* Dark Mod - Avatar Dropdown Menü */
body.tutor-dark-mode .tutor-avatar-dropdown,
html[data-theme="dark"] .tutor-avatar-dropdown {
    background-color: #121212 !important;
    border: 1px solid #2A2A2A !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* Dropdown menü açık olduğunda */
.tutor-avatar-dropdown.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateY(0) !important;
    pointer-events: auto !important; /* Dropdown açıkken tıklanabilirliği etkinleştir */
}

/* Dropdown menü üst kısmı - kullanıcı bilgileri */
.tutor-avatar-dropdown-header {
    padding: 15px !important;
    border-bottom: 1px solid var(--tutor-gray-light, #eee) !important;
    display: flex !important;
    align-items: center !important;
    background: linear-gradient(135deg, var(--tutor-gray-lighter, #f8f9fa), #ffffff) !important;
}

/* Dark Mod - Avatar Dropdown Header */
body.tutor-dark-mode .tutor-avatar-dropdown-header,
html[data-theme="dark"] .tutor-avatar-dropdown-header {
    background: linear-gradient(135deg, #222222, #1a1a1a) !important;
    border-bottom: 1px solid #2A2A2A !important;
}

/* Dropdown menü içindeki avatar */
.tutor-avatar-dropdown-header .tutor-avatar {
    width: 45px !important;
    height: 45px !important;
    margin-right: 12px !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    border-radius: var(--tutor-radius-full, 50%) !important;
    border: 2px solid var(--tutor-primary-light, rgba(67, 97, 238, 0.1)) !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    box-shadow: var(--tutor-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.05)) !important;
}

/* Dropdown menü içindeki avatar resmi */
.tutor-avatar-dropdown-header .tutor-avatar img {
    margin: 0 auto !important;
    display: block !important;
}

/* Dropdown menü içindeki kullanıcı bilgileri */
.tutor-avatar-dropdown-header .tutor-user-info {
    flex: 1 !important;
}

/* Dropdown menü içindeki kullanıcı adı */
.tutor-avatar-dropdown-header .tutor-user-name {
    font-size: 15px !important;
    font-weight: 600 !important;
    color: var(--tutor-dark, #212327) !important;
    margin: 0 !important;
    transition: all 0.3s ease !important;
}

/* Dark Mod - Dropdown Menü Kullanıcı Adı */
body.tutor-dark-mode .tutor-avatar-dropdown-header .tutor-user-name,
html[data-theme="dark"] .tutor-avatar-dropdown-header .tutor-user-name {
    color: #ffffff !important;
}

/* Dropdown menü içindeki kullanıcı rolü */
.tutor-avatar-dropdown-header .tutor-user-role {
    font-size: 12px !important;
    color: var(--tutor-gray, #757C8E) !important;
    margin: 0 !important;
    transition: all 0.3s ease !important;
    opacity: 0.8 !important;
}

/* Dark Mod - Dropdown Menü Kullanıcı Rolü */
body.tutor-dark-mode .tutor-avatar-dropdown-header .tutor-user-role,
html[data-theme="dark"] .tutor-avatar-dropdown-header .tutor-user-role {
    color: #BBBBBB !important;
    opacity: 0.7 !important;
}

/* Yıldız alanı */
.tutor-avatar-dropdown-stats {
    padding: 12px 15px !important;
    border-bottom: 1px solid var(--tutor-gray-light, #eee) !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    background-color: var(--tutor-primary-lighter, rgba(67, 97, 238, 0.05)) !important;
}

/* Yıldız alanındaki yıldızlar */
.tutor-avatar-dropdown-stats .tutor-dashboard-header-ratings {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Yıldız alanındaki yıldızların boyutu */
.tutor-avatar-dropdown-stats .tutor-ratings-stars i {
    font-size: 16px !important;
}

/* Dropdown menü içindeki menü öğeleri listesi */
.tutor-avatar-dropdown-menu {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Dropdown kapalıyken menü öğelerinin tıklanabilirliğini engelle */
.tutor-avatar-dropdown:not(.show) .tutor-avatar-dropdown-menu,
.tutor-avatar-dropdown:not(.show) .tutor-avatar-dropdown-header {
    pointer-events: none !important;
}

/* Dropdown menü içindeki menü öğeleri */
.tutor-avatar-dropdown-menu li {
    padding: 0 !important;
    margin: 0 !important;
}

/* Dropdown menü içindeki menü öğesi bağlantıları */
.tutor-avatar-dropdown-menu li a {
    display: flex !important;
    align-items: center !important;
    padding: 12px 15px !important;
    color: #212327 !important;
    text-decoration: none !important;
    font-size: 14px !important;
    transition: background-color 0.2s ease !important;
}

/* Dark Mod - Dropdown Menü Öğeleri */
body.tutor-dark-mode .tutor-avatar-dropdown-menu li a,
html[data-theme="dark"] .tutor-avatar-dropdown-menu li a {
    color: #ffffff !important;
}

/* Dropdown menü içindeki menü öğesi bağlantılarına hover efekti */
.tutor-avatar-dropdown-menu li a:hover {
    background-color: var(--tutor-primary-lighter, #f8f9fa) !important;
    color: var(--tutor-primary, #3E64DE) !important;
    transform: translateX(5px) !important;
}

/* Dark Mod - Dropdown Menü Öğeleri Hover */
body.tutor-dark-mode .tutor-avatar-dropdown-menu li a:hover,
html[data-theme="dark"] .tutor-avatar-dropdown-menu li a:hover {
    background-color: rgba(50, 50, 50, 0.5) !important;
    color: #ffffff !important;
}

/* Dropdown menü içindeki menü öğesi ikonları */
.tutor-avatar-dropdown-menu li a i {
    margin-right: 10px !important;
    font-size: 16px !important;
    width: 20px !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
}

/* Hover durumunda ikonları hareket ettir */
.tutor-avatar-dropdown-menu li a:hover i {
    transform: scale(1.2) !important;
    color: var(--tutor-primary, #3E64DE) !important;
}

/* Dropdown menü içindeki ayırıcı çizgi */
.tutor-avatar-dropdown-divider {
    height: 1px !important;
    background-color: #eee !important;
    margin: 5px 0 !important;
}

/* Dark Mod - Dropdown Menü Ayırıcı Çizgi */
body.tutor-dark-mode .tutor-avatar-dropdown-divider,
html[data-theme="dark"] .tutor-avatar-dropdown-divider {
    background-color: #2A2A2A !important;
}

/* Çıkış yap butonu için özel stil */
.tutor-avatar-dropdown-menu li a.tutor-avatar-dropdown-logout {
    color: var(--tutor-danger, #d63638) !important;
    transition: all 0.3s ease !important;
}

.tutor-avatar-dropdown-menu li a.tutor-avatar-dropdown-logout:hover {
    background-color: rgba(239, 71, 111, 0.1) !important;
    color: var(--tutor-danger, #d63638) !important;
    transform: translateX(5px) !important;
}

/* Dropdown menü üçgen işareti (ok) */
.tutor-avatar-dropdown:before {
    content: "" !important;
    position: absolute !important;
    top: -8px !important;
    right: 15px !important;
    width: 16px !important;
    height: 16px !important;
    background-color: var(--tutor-gray-lighter, #f8f9fa) !important;
    transform: rotate(45deg) !important;
    border-top: 1px solid var(--tutor-gray-light, #eee) !important;
    border-left: 1px solid var(--tutor-gray-light, #eee) !important;
    z-index: -1 !important;
    box-shadow: var(--tutor-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.05)) !important;
}

/* Dark Mod - Dropdown Menü Üçgen İşareti (Ok) */
body.tutor-dark-mode .tutor-avatar-dropdown:before,
html[data-theme="dark"] .tutor-avatar-dropdown:before {
    background-color: #222222 !important;
    border-top: 1px solid #2A2A2A !important;
    border-left: 1px solid #2A2A2A !important;
    box-shadow: var(--tutor-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.2)) !important;
}

/* Mobil uyumluluk */
@media (max-width: 767px) {
    .tutor-avatar-dropdown {
        width: 200px !important;
    }
}


/*--------------------------------------------------------------
9. EKLENEN CSS DOSYALARI - DOĞRUDAN BUTON DÜZELTMESİ
--------------------------------------------------------------*/

/**
 * Doğrudan Buton Düzeltmesi
 * Bu CSS dosyası, "Kurs Oluştur" butonunu doğrudan hedefleyen CSS kuralları içerir.
 * !important kullanarak diğer stilleri geçersiz kılar.
 */

/* Doğrudan buton seçicisi */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course {
    color: var(--tutor-color-primary) !important;
    background-color: var(--tutor-light-primary) !important;
    background-image: none !important;
    box-shadow: none !important;
    transform: none !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border: none !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
    display: inline-flex !important;
    align-items: center !important;
}

/* Hover durumu */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
    color: var(--tutor-color-primary) !important;
    transform: none !important;
    box-shadow: none !important;
}

/* İkon */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i {
    color: var(--tutor-color-primary) !important;
    margin-right: 8px !important;
}

/* Inline stil geçersiz kılma */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course[style] {
    color: var(--tutor-color-primary) !important;
    background-color: var(--tutor-light-primary) !important;
    border: none !important;
}

/* Inline stil geçersiz kılma - hover */
.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course[style]:hover {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
    color: var(--tutor-color-primary) !important;
}

/*--------------------------------------------------------------
10. EKLENEN CSS DOSYALARI - BAŞLIK ANİMASYONLARINI DEVRE DIŞI BIRAKMA
--------------------------------------------------------------*/

/**
 * Başlık Animasyonlarını Devre Dışı Bırakma
 * Bu CSS dosyası, dashboard sayfalarındaki başlık animasyonlarını devre dışı bırakır.
 * Sayfa başlıkları sabit boyutta kalır ve küçülme/büyüme animasyonu olmaz.
 * NOT: Sayfa geçiş animasyonları korunur.
 */

/* Tüm dashboard başlıklarının animasyonlarını kaldır */
body.tutor-dashboard-page .tutor-fs-5,
body.tutor-dashboard-page .tutor-fs-4,
body.tutor-dashboard-page .tutor-fs-3,
body.tutor-dashboard-page h1,
body.tutor-dashboard-page h2,
body.tutor-dashboard-page h3,
body.tutor-dashboard-page .tutor-dashboard-title,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5,
body.tutor-dashboard-page .header-title,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-16,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24,
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-16 {
    /* Sadece başlık animasyonlarını kaldır, diğer animasyonları koru */
    opacity: 1 !important;
    visibility: visible !important;
}

/* Sayfa başlıklarını özellikle hedefle */
body.tutor-dashboard-page .tutor-dashboard-content-inner h3,
body.tutor-dashboard-page .tutor-dashboard-content-inner h2,
body.tutor-dashboard-page .tutor-dashboard-content-inner h1,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-4,
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-3 {
    /* Sadece başlık animasyonlarını kaldır, diğer animasyonları koru */
    opacity: 1 !important;
    visibility: visible !important;
}

/* Özel sayfa başlıklarını hedefle */
body.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black:not(.tutor-dashboard-menu-item-text):not(.tutor-dashboard-menu-item-link) {
    /* Sadece başlık animasyonlarını kaldır, diğer animasyonları koru */
    opacity: 1 !important;
    visibility: visible !important;
}

/* İstek Listesi, Yorumlar, Test Katılımlarım, Sipariş Geçmişi, Soru ve Cevap, Para Çekme başlıkları */
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5:contains("İstek Listesi"),
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5:contains("Yorumlar"),
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5:contains("Test Katılımlarım"),
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5:contains("Sipariş Geçmişi"),
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5:contains("Soru ve Cevap"),
body.tutor-dashboard-page .tutor-dashboard-content-inner .tutor-fs-5:contains("Para Çekme") {
    /* Sadece başlık animasyonlarını kaldır, diğer animasyonları koru */
    opacity: 1 !important;
    visibility: visible !important;
}

/*--------------------------------------------------------------
11. EKLENEN CSS DOSYALARI - HEADER'DAKİ KURS OLUŞTUR BUTONUNU GİZLEME
--------------------------------------------------------------*/

/**
 * Header'daki Kurs Oluştur Butonunu Gizleme
 * Bu CSS dosyası, header'daki "Kurs Oluştur" butonunu gizler.
 * Buton artık "Kurslarım" sayfasında görünecektir.
 */

/* Header'daki Kurs Oluştur butonunu gizle */
.tutor-header-right-side a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
.tutor-header-right-side a[href*="kurs-olustur"].tutor-btn.tutor-btn-outline-primary,
.tutor-header-right-side a[href*="course-create"].tutor-btn.tutor-btn-outline-primary,
.tutor-header-right-side .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
.tutor-frontend-dashboard-header a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
.tutor-frontend-dashboard-header .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
#tutor-page-wrap > div > div.tutor-container.scroll-at-max > div.tutor-row.tutor-d-flex.tutor-justify-between.tutor-frontend-dashboard-header > div.tutor-header-right-side.tutor-col-md-6.tutor-d-flex.tutor-justify-end.tutor-mt-20.tutor-mt-md-0 > div.tutor-d-flex.tutor-align-center.scroll-at-max > a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Kurslarım sayfasındaki Kurs Oluştur butonunun stilini düzenle */
.tutor-dashboard-my-courses .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: var(--tutor-color-primary) !important;
    background-color: var(--tutor-light-primary) !important;
    border: none !important;
    box-shadow: none !important;
    transition: all 0.3s ease !important;
}

/* Kurslarım sayfasındaki Kurs Oluştur butonunun hover durumu */
.tutor-dashboard-my-courses .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
    color: var(--tutor-color-primary) !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Kurslarım sayfasındaki Kurs Oluştur butonunun içindeki ikon */
.tutor-dashboard-my-courses .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i {
    color: var(--tutor-color-primary) !important;
    margin-right: 8px !important;
}

/*--------------------------------------------------------------
12. EKLENEN CSS DOSYALARI - ADMİN YAZISI VE YILDIZLARI GİZLEME
--------------------------------------------------------------*/

/**
 * Admin yazısı ve yıldızları gizleme stilleri
 * Bu dosya, dashboard header'daki admin yazısını ve yıldızları gizlemek için kullanılır.
 * Dropdown menü içindeki elementleri etkilemez.
 */

/* Sadece header'daki admin yazısını gizle */
.tutor-frontend-dashboard-header .tutor-header-right-side .tutor-user-info .tutor-fs-4,
.tutor-frontend-dashboard-header .tutor-header-right-side .tutor-user-info .tutor-dashboard-header-username {
    display: none !important;
}

/* Sadece header'daki yıldızları gizle */
.tutor-frontend-dashboard-header .tutor-header-right-side .tutor-dashboard-header-stats,
.tutor-frontend-dashboard-header .tutor-header-right-side .tutor-dashboard-header-ratings {
    display: none !important;
}

/* Dropdown menüdeki elementleri göster */
.tutor-avatar-dropdown .tutor-user-name {
    display: block !important;
}

/* Profil yanındaki kullanıcı adı stili */
.tutor-user-name-small {
    font-size: 12px !important;
    color: var(--tutor-color-secondary) !important;
    font-weight: normal !important;
    margin-left: 5px !important;
    opacity: 0.8 !important;
}

/* Dashboard form kontrol elementi için arka plan rengi */
body.tutor-dashboard-page .tutor-form-control {
    background-color: var(--tutor-light-primary);
}

/* Dropdown menüdeki yıldızları göster */
.tutor-avatar-dropdown .tutor-avatar-dropdown-stats {
    display: flex !important;
    padding: 10px 15px !important;
    border-bottom: 1px solid #eee !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Dark Mod - Dropdown Menü Yıldız Alanı Alt Çizgisi */
body.tutor-dark-mode .tutor-avatar-dropdown .tutor-avatar-dropdown-stats,
html[data-theme="dark"] .tutor-avatar-dropdown .tutor-avatar-dropdown-stats {
    border-bottom: 1px solid #2A2A2A !important;
}

/* Dark Mod - Profil yanındaki kullanıcı adı stili */
body.tutor-dark-mode .tutor-user-name-small,
html[data-theme="dark"] .tutor-user-name-small {
    color: #BBBBBB !important;
    opacity: 0.7 !important;
}

.tutor-avatar-dropdown .tutor-avatar-dropdown-stats .tutor-dashboard-header-ratings,
.tutor-avatar-dropdown .tutor-avatar-dropdown-stats .tutor-ratings,
.tutor-avatar-dropdown .tutor-avatar-dropdown-stats .tutor-ratings-stars {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.tutor-avatar-dropdown .tutor-avatar-dropdown-stats .tutor-ratings-stars i {
    display: inline-block !important;
    font-size: 16px !important;
}



/*--------------------------------------------------------------
13. EKLENEN CSS DOSYALARI - KONTROL PANELİ MENÜ ÖĞESİ DÜZELTMESİ
--------------------------------------------------------------*/

/**
 * Kontrol Paneli Menü Öğesi Düzeltmesi
 * Bu CSS dosyası, kontrol paneli menü öğesinin rengini diğer menü öğeleriyle aynı yapar.
 */

/* Kontrol Paneli menü öğesi için özel stil */
.tutor-dashboard-menu-item-index .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item-index i,
.tutor-dashboard-menu-item-index .tutor-icon-dashboard {
    color: var(--tutor-color-primary) !important;
}

/* Kontrol Paneli menü öğesi hover durumu */
.tutor-dashboard-menu-item-index .tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item-index .tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-index .tutor-dashboard-menu-item-link:hover .tutor-icon-dashboard {
    color: var(--tutor-color-primary) !important;
}

/* Kontrol Paneli menü öğesi aktif durumu */
.tutor-dashboard-menu-item-index.active .tutor-dashboard-menu-item-link .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item-index.active .tutor-dashboard-menu-item-link i,
.tutor-dashboard-menu-item-index.active .tutor-dashboard-menu-item-link .tutor-icon-dashboard {
    color: var(--tutor-color-primary) !important;
}

/* Kontrol Paneli menü öğesi metin rengi */
.tutor-dashboard-menu-item-index .tutor-dashboard-menu-item-text {
    color: var(--tutor-color-primary) !important;
}

/*--------------------------------------------------------------
14. EKLENEN CSS DOSYALARI - KURS OLUŞTUR BUTONU İÇİN ÖZEL STİL
--------------------------------------------------------------*/

/**
 * Kurs Oluştur Butonu İçin Özel Stil
 * Bu CSS dosyası, "Kurs Oluştur" butonunun stilini düzeltmek için oluşturulmuştur.
 * Buton için var(--tutor-color-primary) değişkenini kullanarak tema renkleriyle uyumlu hale getirir.
 */

/* Kurs Oluştur butonu için özel stil - Çok spesifik seçiciler */
.tutor-header-right-side a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
.tutor-header-right-side a[href*="kurs-olustur"].tutor-btn.tutor-btn-outline-primary,
.tutor-header-right-side a[href*="course-create"].tutor-btn.tutor-btn-outline-primary,
.tutor-header-right-side .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
.tutor-frontend-dashboard-header a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
.tutor-frontend-dashboard-header .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course {
    color: var(--tutor-color-primary) !important;
    background-color: var(--tutor-light-primary) !important;
    border: none !important;
    background-image: none !important;
    box-shadow: none !important;
    transform: none !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
    display: inline-flex !important;
    align-items: center !important;
}

/* Kurs Oluştur butonunun hover durumu */
.tutor-header-right-side a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover,
.tutor-header-right-side a[href*="kurs-olustur"].tutor-btn.tutor-btn-outline-primary:hover,
.tutor-header-right-side a[href*="course-create"].tutor-btn.tutor-btn-outline-primary:hover,
.tutor-header-right-side .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover,
.tutor-frontend-dashboard-header a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover,
.tutor-frontend-dashboard-header .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover {
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
    color: var(--tutor-color-primary) !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Kurs Oluştur butonunun içindeki ikon */
.tutor-header-right-side a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i,
.tutor-header-right-side .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i,
.tutor-frontend-dashboard-header a.tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i,
.tutor-frontend-dashboard-header .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i {
    color: var(--tutor-color-primary) !important;
    margin-right: 8px !important;
}

/* Dark mod için Dashboard Kurs Oluştur butonu */
body.tutor-dark-mode.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course {
    background-color: rgb(255 255 255 / 7%) !important;
    color: #FFFFFF !important;
    border: none !important;
}

/* Dark mod için Dashboard Kurs Oluştur butonunun hover durumu */
body.tutor-dark-mode.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course:hover {
    background-color: rgb(130 130 130 / 50%) !important;
}

/* Dark mod için Dashboard Kurs Oluştur butonunun içindeki ikon */
body.tutor-dark-mode.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary.tutor-create-new-course i {
    color: #FFFFFF !important;
}

/*--------------------------------------------------------------
15. EKLENEN CSS DOSYALARI - ÇIKIŞ YAP BUTONU HOVER DÜZELTMESİ
--------------------------------------------------------------*/

/**
 * Çıkış Yap Butonu Hover Düzeltmesi
 * Bu CSS dosyası, sidebar'daki ve dropdown menüdeki "Çıkış Yap" butonlarının hover durumunda ikonun da yazı gibi aynı renkte olmasını sağlar.
 */

/* Sidebar'daki Çıkış Yap butonunun hover durumunda ikonun da yazı gibi aynı renkte olmasını sağla */
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link:hover i {
    color: var(--tutor-danger, #d63638) !important;
}

/* Sidebar'daki ayırıcı başlıkları dark modda koyulaştır */
body.tutor-dark-mode .tutor-dashboard-menu-divider-header,
html[data-theme="dark"] .tutor-dashboard-menu-divider-header {
    color: #BBBBBB !important;
}

/* Sidebar'daki Çıkış Yap butonunun hover durumu */
.tutor-dashboard-menu-item:last-child .tutor-dashboard-menu-item-link:hover {
    background-color: rgba(239, 71, 111, 0.1) !important;
    color: var(--tutor-danger, #d63638) !important;
    transform: translateX(5px) !important;
}

/* Avatar dropdown menüsündeki Çıkış Yap butonunun hover durumunda ikonun da yazı gibi aynı renkte olmasını sağla */
.tutor-avatar-dropdown-menu li:last-child a:hover .tutor-dashboard-menu-item-icon,
.tutor-avatar-dropdown-menu li:last-child a:hover i {
    color: var(--tutor-danger, #d63638) !important;
}

/* Avatar dropdown menüsündeki Çıkış Yap butonunun hover durumu */
.tutor-avatar-dropdown-menu li:last-child a:hover {
    background-color: rgba(239, 71, 111, 0.1) !important;
    color: var(--tutor-danger, #d63638) !important;
    transform: translateX(5px) !important;
}

/*--------------------------------------------------------------
16. EKLENEN CSS DOSYALARI - SAYFA GEÇİŞ ANİMASYONLARINI KORUMA
--------------------------------------------------------------*/

/**
 * Sayfa Geçiş Animasyonlarını Koruma
 * Bu CSS dosyası, sayfa geçiş animasyonlarını korur ve başlık animasyonlarını devre dışı bırakır.
 */

/* Sayfa geçiş animasyonlarını koru */
.tutor-dashboard-page .tutor-dashboard-content-inner,
.tutor-dashboard-page .tutor-dashboard-content,
.tutor-dashboard-page .tutor-row,
.tutor-dashboard-page .tutor-col,
.tutor-dashboard-page .tutor-card,
.tutor-dashboard-page .tutor-table,
.tutor-dashboard-page .tutor-list {
    transition: opacity 0.3s ease, transform 0.3s ease !important;
}

/* Sayfa yüklendiğinde animasyon efekti */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Sayfa içeriği için animasyon */
.tutor-dashboard-page .tutor-dashboard-content-inner {
    animation: fadeIn 0.3s ease-out !important;
}

/* Başlık animasyonlarını devre dışı bırak */
.tutor-dashboard-page .tutor-fs-5,
.tutor-dashboard-page .tutor-dashboard-title,
.tutor-dashboard-page h1.tutor-fs-5,
.tutor-dashboard-page h2.tutor-fs-5,
.tutor-dashboard-page h3.tutor-fs-5,
.tutor-dashboard-page .header-title,
.tutor-dashboard-page .tutor-fs-5.tutor-fw-medium.tutor-color-black {
    animation: none !important;
    transform: none !important;
    transition: none !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/*--------------------------------------------------------------
17. EKLENEN CSS DOSYALARI - SIDEBAR TAMAMLAMA İKONLARI DÜZELTMESİ
--------------------------------------------------------------*/

/**
 * Sidebar Tamamlama İkonları Düzeltmesi
 * Bu CSS dosyası, sidebar'daki ders tamamlama ikonlarının konumunu sabitler.
 * Böylece ders başlığının uzunluğuna bağlı olmadan ikonlar aynı hizada görünür.
 */

/* Sidebar ders öğelerinin yapısını düzenle */
.tutor-course-topic-item a {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    position: relative !important;
    padding-right: 40px !important; /* Sağ tarafta ikon için yer aç */
}

/* Sol taraftaki içerik (thumbnail ve başlık) */
.tutor-course-topic-item a .tutor-d-flex.tutor-mr-32 {
    flex: 1 !important;
    min-width: 0 !important; /* Taşmaları önlemek için */
    margin-right: 8px !important; /* İkon ile arasında boşluk bırak */
}

/* Sağ taraftaki tamamlama ikonu */
.tutor-course-topic-item a .tutor-d-flex.tutor-ml-auto {
    position: absolute !important;
    right: 12px !important; /* Sağdan mesafe - 10px'den 12px'e güncellendi */
    top: 50% !important;
    transform: translateY(-50%) !important;
    margin-left: 0 !important; /* ml-auto'yu sıfırla */
    min-width: 24px !important; /* Sabit genişlik */
    justify-content: center !important;
}

/* Checkbox'ların görünümünü iyileştir */
.tutor-course-topic-item .tutor-form-check-input {
    margin: 0 !important;
    position: relative !important;
    right: 0 !important;
}

/* Kilit ikonunun görünümünü iyileştir */
.tutor-course-topic-item .tutor-icon-lock-line {
    margin: 0 !important;
    position: relative !important;
    right: 0 !important;
}

/* Aktif ders öğesi için özel stil */
.tutor-course-topic-item.is-active a .tutor-d-flex.tutor-ml-auto {
    right: 12px !important; /* Aktif durumda da aynı konumda kalsın - 10px'den 12px'e güncellendi */
}

/* Başlık alanının taşmasını önle */
.tutor-course-topic-item-title {
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    max-width: 100% !important; /* Mevcut genişliğe göre ayarla */
}

/* Süre bilgisinin taşmasını önle */
.tutor-course-topic-item-duration {
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    max-width: 100% !important;
}

/* Başlık ve süre bilgisini içeren wrapper */
.tutor-lesson-content-title-wrap {
    min-width: 0 !important; /* Taşmaları önlemek için */
    max-width: calc(100% - 10px) !important; /* Sağ tarafta biraz boşluk bırak */
}

/* İçerik alanındaki tamamlama işaretlerinin margin-top değerini sıfırla */
.tutor-course-single-content-wrapper input.tutor-form-check-input.tutor-form-check-circle {
    margin-top: 0 !important; /* margin-top: 10px değerini geçersiz kıl */
}

/*--------------------------------------------------------------
18. EKLENEN CSS DOSYALARI - SIDEBAR HOVER DÜZELTMESİ
--------------------------------------------------------------*/

/**
 * Sidebar Hover Düzeltmesi
 * Bu CSS dosyası, sidebar menü öğelerinin hover durumundaki rengini değişken rengine göre ayarlar.
 * Böylece tüm hover efektleri tema renkleriyle uyumlu olur.
 */

/* Tüm sidebar menü öğelerinin hover durumu */
.tutor-dashboard-menu-item-link:hover {
    background-color: var(--tutor-primary-lighter) !important;
    color: var(--tutor-color-primary) !important;
    transform: translateX(5px) !important;
}

/* Sidebar menü ikonlarının hover durumu */
.tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-link:hover .tutor-icon-dashboard,
.tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon {
    color: var(--tutor-color-primary) !important;
}

/* Yorumlar menü öğesinin hover durumu - özel seçici */
.tutor-dashboard-menu-item-yorumlar .tutor-dashboard-menu-item-link:hover,
.tutor-dashboard-menu-item-reviews .tutor-dashboard-menu-item-link:hover {
    background-color: var(--tutor-primary-lighter) !important;
    color: var(--tutor-color-primary) !important;
    transform: translateX(5px) !important;
}

/* Yorumlar menü öğesi ikonunun hover durumu - özel seçici */
.tutor-dashboard-menu-item-yorumlar .tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-yorumlar .tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon,
.tutor-dashboard-menu-item-reviews .tutor-dashboard-menu-item-link:hover i,
.tutor-dashboard-menu-item-reviews .tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-icon {
    color: var(--tutor-color-primary) !important;
}

/* Tüm sidebar menü öğelerinin metin rengi */
.tutor-dashboard-menu-item-link .tutor-dashboard-menu-item-text {
    transition: var(--tutor-transition) !important;
}

/* Tüm sidebar menü öğelerinin hover durumundaki metin rengi */
.tutor-dashboard-menu-item-link:hover .tutor-dashboard-menu-item-text {
    color: var(--tutor-color-primary) !important;
}

/*--------------------------------------------------------------
19. EKLENEN CSS DOSYALARI - GÖRÜNÜRLÜK DÜZELTMELERİ
--------------------------------------------------------------*/

/**
 * GÖRÜNÜRLÜK DÜZELTMELERİ
 * Bu dosya, dashboard'daki bazı elementlerin görünürlük sorunlarını düzeltmek için kullanılır.
 */

/* Tüm içerik elementlerinin görünürlüğünü sağla */
.tutor-dashboard-content-inner,
.tutor-dashboard-content-inner *,
.tutor-frontend-dashboard-course-progress,
.tutor-frontend-dashboard-course-progress *,
.tutor-course-listing-item,
.tutor-course-listing-item *,
.tutor-course-card,
.tutor-course-card * {
    opacity: 1 !important;
    visibility: visible !important;
}


.tutor-course-card:hover,
.tutor-course-listing-item:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 15px rgba(67, 97, 238, 0.15) !important;
}

/* Kurs resimleri için düzeltmeler */
.tutor-course-thumbnail,
.tutor-course-thumbnail a {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    overflow: hidden !important;
    border-radius: 8px 8px 0 0 !important;
    position: relative !important;
}

.tutor-course-thumbnail img {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    width: 100% !important;
    height: auto !important;
    object-fit: cover !important;
    transition: transform 0.5s ease !important;
}

.tutor-course-thumbnail:hover img {
    transform: scale(1.1) !important;
}

/* Tablo görünürlüğü düzeltmeleri */
.tutor-table,
.tutor-table *,
.tutor-table tr,
.tutor-table td,
.tutor-table th {
    opacity: 1 !important;
    visibility: visible !important;
}


.tutor-table tr {
    display: table-row !important;
    transition: all 0.3s ease !important;
}

.tutor-table tr:hover {
    background-color: rgba(67, 97, 238, 0.05) !important;
}

/* Dark mod için tablo satırlarının arka plan rengi */
body.tutor-dark-mode .tutor-table tr td,
html[data-theme="dark"] .tutor-table tr td {
    background: #1a1a1a !important;
}

/* Dark mod için tablo satırlarının hover durumu */
body.tutor-dark-mode .tutor-table tr:hover,
html[data-theme="dark"] .tutor-table tr:hover {
    background-color: rgba(50, 50, 50, 0.5) !important;
}

/* Dark mod için dashboard butonları */
body.tutor-dark-mode.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary {
    background-color: rgb(255 255 255 / 7%) !important;
    color: #FFFFFF !important;
    border: none !important;
}

/* Dark mod için dashboard butonlarının hover durumu */
body.tutor-dark-mode.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary:hover,
html[data-theme="dark"] body.tutor-dashboard-page .tutor-btn.tutor-btn-outline-primary:hover {
    background-color: rgb(130 130 130 / 50%) !important;
}

/* Tablo içindeki bağlantılar için dark mod rengi */
body.tutor-dark-mode .tutor-table tr td>a:not(.tutor-btn):not(.tutor-iconic-btn):not(.quiz-manual-review-action),
body.tutor-dark-mode .tutor-table tr td .tutor-table-link,
html[data-theme="dark"] .tutor-table tr td>a:not(.tutor-btn):not(.tutor-iconic-btn):not(.quiz-manual-review-action),
html[data-theme="dark"] .tutor-table tr td .tutor-table-link {
    color: #ffffff !important;
}

/* Kurs kartlarındaki kurs isimleri için dark mod rengi */
body.tutor-dark-mode .tutor-course-card .tutor-course-name,
body.tutor-dark-mode .tutor-course-card .tutor-course-name a,
html[data-theme="dark"] .tutor-course-card .tutor-course-name,
html[data-theme="dark"] .tutor-course-card .tutor-course-name a {
    color: #ffffff !important;
}

/* Navigasyon linkleri için dark mod rengi */
body.tutor-dark-mode .tutor-nav-link,
html[data-theme="dark"] .tutor-nav-link {
    color: #ffffff8a !important;
}

/* Aktif navigasyon linkleri için dark mod rengi */
body.tutor-dark-mode .tutor-nav-link.is-active,
html[data-theme="dark"] .tutor-nav-link.is-active {
    color: #ffffff !important;
}


/* Kurslarım tablosu için özel stiller */
.tutor-dashboard-content-inner table {
    display: table !important;
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    margin-bottom: 30px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid #e9ecef !important;
}

/* Dark mod için dashboard içerik tablosu kenarlık rengi */
body.tutor-dark-mode .tutor-dashboard-content-inner table,
html[data-theme="dark"] .tutor-dashboard-content-inner table {
    border: 1px solid #0f0f0f !important;
}

.tutor-dashboard-content-inner table tr {
    display: table-row !important;
    transition: all 0.3s ease !important;
}

.tutor-dashboard-content-inner table tr:hover {
    background-color: rgba(67, 97, 238, 0.05) !important;
}

.tutor-dashboard-content-inner table th {
    display: table-cell !important;
    padding: 15px !important;
    font-weight: 600 !important;
    color: #212327 !important;
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #e9ecef !important;
    text-align: left !important;
}

.tutor-dashboard-content-inner table td {
    display: table-cell !important;
    padding: 15px !important;
    color: #6c757d !important;
    vertical-align: middle !important;
}

/* Liste görünürlüğü düzeltmeleri */
.tutor-list,
.tutor-list * {
    opacity: 1 !important;
    visibility: visible !important;
}

/* Animasyon düzeltmeleri */
.tutor-animate-card,
.tutor-profile-title,
.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-mb-24 {
    opacity: 1 !important;
    animation: none !important;
    transform: none !important;
    transition: none !important;
}

/* Devam Eden Kurslar bölümü düzeltmeleri */
.tutor-frontend-dashboard-course-progress {
    display: block !important;
    margin-top: 30px !important;
}

/* Devam Eden Kurslar başlığı düzeltmesi */
.tutor-fs-5.tutor-fw-medium.tutor-color-black.tutor-text-capitalize.tutor-mb-24 {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    margin-bottom: 24px !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    color: #212327 !important;
}

/* İlerleme çubuğu düzeltmeleri */
.tutor-progress-bar {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: 8px !important;
    background-color: #e9ecef !important;
    border-radius: 999px !important;
    overflow: hidden !important;
    margin: 10px 0 !important;
}

.tutor-progress-value {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: 100% !important;
    background: linear-gradient(90deg, var(--tutor-color-primary), var(--tutor-color-primary)) !important;
    border-radius: 999px !important;
}

/* Kurs kartları için düzeltmeler */
.tutor-course-listing-grid-3 .tutor-course-listing-item {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
}

/* Kurs içerik elementleri için düzeltmeler */
.tutor-course-listing-item-title,
.tutor-course-listing-item-title a,
.tutor-course-card-title,
.tutor-course-card-title a,
.tutor-course-card .tutor-course-name,
.tutor-course-card .tutor-course-name a {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #212327 !important;
    margin: 15px 0 10px !important;
    /* padding: 0 15px !important; */ /* Padding kaldırıldı */
    text-decoration: none !important;
    transition: color 0.3s ease !important;
    line-height: 1.4 !important;
}

.tutor-course-listing-item-title a:hover,
.tutor-course-card-title a:hover {
    color: #4361ee !important;
}

.tutor-course-listing-item-meta,
.tutor-course-listing-item-meta *,
.tutor-course-card-meta,
.tutor-course-card-meta * {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    font-size: 14px !important;
    color: #6c757d !important;
    margin: 5px 0 !important;
    padding: 0 15px !important;
}

.tutor-course-listing-item-footer,
.tutor-course-listing-item-footer *,
.tutor-course-card-footer,
.tutor-course-card-footer * {
    opacity: 1 !important;
    visibility: visible !important;
    display: block !important;
    padding: 15px !important;
    border-top: 1px solid #e9ecef !important;
    margin-top: 10px !important;
}

/* Flex container düzeltmeleri */
.tutor-course-listing-grid {
    display: flex !important;
    flex-wrap: wrap !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Yıldız derecelendirme düzeltmeleri */
.tutor-ratings,
.tutor-ratings-stars,
.tutor-ratings-average,
.tutor-ratings-count,
.tutor-star-rating-group,
.tutor-star-rating-group *,
.tutor-ratings-container,
.tutor-ratings-container * {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.tutor-ratings-stars,
.tutor-star-rating-group {
    color: #f9a134 !important;
    font-size: 14px !important;
    margin-right: 5px !important;
    letter-spacing: 2px !important;
}

.tutor-ratings-average,
.tutor-ratings-count {
    font-size: 14px !important;
    color: #6c757d !important;
}

/* Avatar dropdown menüsündeki yıldız derecelendirme ortalaması için margin-left kaldırma */
.tutor-avatar-dropdown .tutor-avatar-dropdown-stats .tutor-ratings-average {
    margin-left: 0 !important;
}

/* Kurslarım tablosundaki yıldız derecelendirmeleri */
.tutor-dashboard-content-inner table .tutor-star-rating-group {
    display: flex !important;
    align-items: center !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.tutor-dashboard-content-inner table .tutor-icon-star-line,
.tutor-dashboard-content-inner table .tutor-icon-star-full,
.tutor-dashboard-content-inner table .tutor-icon-star-half {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    color: #f9a134 !important;
    font-size: 16px !important;
    margin-right: 2px !important;
}

.tutor-dashboard-content-inner table .tutor-icon-star-line {
    color: #e9ecef !important;
}

/* Tamamlanma yüzdesi düzeltmeleri */
.tutor-course-completion-text {
    font-size: 14px !important;
    color: #6c757d !important;
    margin: 5px 0 !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Kurs tamamlanma sayısı düzeltmeleri */
.tutor-course-completed-text {
    font-size: 14px !important;
    color: #6c757d !important;
    margin: 5px 0 !important;
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Devam Et butonu düzeltmeleri */
.tutor-btn.tutor-btn-outline-primary {
    display: inline-block !important;
    opacity: 1 !important;
    visibility: visible !important;
    padding: 8px 16px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    color: var(--tutor-color-primary) !important;
    background-color: var(--tutor-light-primary) !important;
    border: none !important;
    border-radius: 6px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    text-align: center !important;
}

.tutor-btn.tutor-btn-outline-primary:hover {
    color: var(--tutor-color-primary) !important;
    background-color: color-mix(in srgb, var(--tutor-color-primary) 10%, transparent) !important;
}

/*--------------------------------------------------------------
20. EKLENEN CSS DOSYALARI - SIDEBAR GÖRÜNÜRLÜK AYARLARI
--------------------------------------------------------------*/

/**
 * SIDEBAR GÖRÜNÜRLÜK AYARLARI
 * Bu bölüm, sidebar'ın hangi ekran boyutlarında görüneceğini ayarlar.
 * Orijinal Tutor LMS'in 767px medya sorgusunu 991px ile değiştirir.
 */

/* Orijinal Tutor LMS medya sorgusunu geçersiz kıl */
@media (max-width: 991px) {
    .tutor-dashboard:not(.is-sidebar-expanded) .tutor-dashboard-left-menu {
        display: none;
    }
}

/*--------------------------------------------------------------
21. EKLENEN CSS DOSYALARI - TOOLTIP DÜZELTMELERİ
--------------------------------------------------------------*/

/**
 * TOOLTIP DÜZELTMELERİ
 * Bu bölüm, tooltip'lerin hover durumunda görünmesi ile ilgili sorunları düzeltir.
 * Özellikle para çekme sayfasındaki tooltip'lerin düzgün çalışmasını sağlar.
 */

/* Tooltip metni - varsayılan olarak gizli */
.tooltip-txt {
    visibility: hidden !important;
    opacity: 0 !important;
}

/* Hover durumunda tooltip'i göster */
.tooltip-wrap:hover .tooltip-txt {
    visibility: visible !important;
    opacity: 1 !important;
}

/* Tooltip yönleri - sol tooltip için */
.tooltip-left {
    top: 50% !important;
    transform: translateY(-50%) !important;
}

/* Tooltip ok işaretleri - sol tooltip için */
.tooltip-left::after {
    top: 50% !important;
}

/* Para çekme sayfasındaki ünlem ikonu için margin-top kapatma */
.tutor-frontend-dashboard-withdrawal .tutor-mt-12,
.withdraw-history-table-wrap .tutor-mt-12 {
    margin-top: 0 !important;
}

/* Para çekme sayfasındaki ünlem ikonu için daha spesifik seçici */
.tutor-frontend-dashboard-withdrawal .tooltip-icon.tutor-mt-12,
.withdraw-history-table-wrap .tooltip-icon.tutor-mt-12,
.tutor-frontend-dashboard-withdrawal .tutor-badge-label .tutor-mt-12,
.withdraw-history-table-wrap .tutor-badge-label .tutor-mt-12 {
    margin-top: 0 !important;
}

/*--------------------------------------------------------------
22. SORU-CEVAP SAYFASI TOOLTIP GİZLEME
--------------------------------------------------------------*/

/**
 * SORU-CEVAP SAYFASI TOOLTIP'LERİNİ GİZLE
 * Bu bölüm, soru-cevap sayfasındaki sadece öğrenci sütunundaki siyah ok tooltip'lerini gizler.
 * Durum sütunundaki tooltip'lere dokunmaz.
 */

/* Sadece öğrenci sütunundaki (student kolonu) tooltip'leri gizle */
.qna-list-table td:first-child .tooltip-wrap.tooltip-icon-custom.tutor-qna-badges-wrapper {
    display: none !important;
}

/* Daha spesifik seçici - sadece öğrenci sütunu */
.frontend-dashboard-qna-table-instructor td:first-child .tooltip-wrap.tooltip-icon-custom.tutor-qna-badges-wrapper,
.frontend-dashboard-qna-table-student td:first-child .tooltip-wrap.tooltip-icon-custom.tutor-qna-badges-wrapper {
    display: none !important;
}

/* Sadece tutor-qna-badges-wrapper sınıfına sahip tooltip'leri gizle (öğrenci sütunundaki) */
.qna-list-table .tutor-qna-badges-wrapper.tooltip-wrap {
    display: none !important;
}

/*--------------------------------------------------------------
23. DASHBOARD SIDEBAR MENÜ ÖĞELERİNİ GİZLEME
--------------------------------------------------------------*/

/**
 * DASHBOARD SIDEBAR'DAKİ BELİRLİ MENÜ ÖĞELERİNİ GİZLE
 * Bu bölüm, dashboard sidebar'daki istek listesi, yorumlar ve order history sekmelerini gizler.
 * CSS selector'ları kullanarak belirtilen menü öğelerini tamamen gizler.
 */

/* İstek Listesi sekmesini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-wishlist {
    display: none !important;
}

/* Yorumlar sekmesini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-reviews {
    display: none !important;
}

/* Order History (Satın Alma Geçmişi) sekmesini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-purchase_history {
    display: none !important;
}

/* Daha spesifik seçiciler - eğer yukarıdakiler çalışmazsa */
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-wishlist,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-reviews,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-purchase_history {
    display: none !important;
}

/* Alternatif seçiciler - menü öğelerinin farklı yapıları için */
.tutor-dashboard-permalinks .tutor-dashboard-menu-wishlist,
.tutor-dashboard-permalinks .tutor-dashboard-menu-reviews,
.tutor-dashboard-permalinks .tutor-dashboard-menu-purchase_history {
    display: none !important;
}

/* Li elementleri için seçiciler */
li.tutor-dashboard-menu-item.tutor-dashboard-menu-wishlist,
li.tutor-dashboard-menu-item.tutor-dashboard-menu-reviews,
li.tutor-dashboard-menu-item.tutor-dashboard-menu-purchase_history {
    display: none !important;
}

/* Menü linklerini de gizle */
a[href*="wishlist"],
a[href*="reviews"],
a[href*="purchase_history"] {
    display: none !important;
}

/* Dashboard menü container'ı içindeki spesifik linkler */
.tutor-dashboard-left-menu a[href*="wishlist"],
.tutor-dashboard-left-menu a[href*="reviews"],
.tutor-dashboard-left-menu a[href*="purchase_history"] {
    display: none !important;
}

/* Parent li elementlerini de gizle */
.tutor-dashboard-left-menu li:has(a[href*="wishlist"]),
.tutor-dashboard-left-menu li:has(a[href*="reviews"]),
.tutor-dashboard-left-menu li:has(a[href*="purchase_history"]) {
    display: none !important;
}

/* EK MENÜ ÖĞELERİNİ GİZLE - İkinci grup */

/* Divider Header (Ayırıcı başlık) sekmesini gizle */
.tutor-dashboard-menu-divider-header {
    display: none !important;
}

/* My Courses (Kurslarım) sekmesini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses {
    display: none !important;
}

/* Announcements (Duyurular) sekmesini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-announcements {
    display: none !important;
}

/* Withdraw (Para Çekme) sekmesini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw {
    display: none !important;
}

/* Quiz Attempts (Quiz Denemeleri) sekmesini gizle */
.tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts {
    display: none !important;
}

/* Daha spesifik seçiciler - ikinci grup için */
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-divider-header,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-announcements,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw,
#tutor-page-wrap .tutor-dashboard-left-menu .tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts {
    display: none !important;
}

/* Li elementleri için seçiciler - ikinci grup */
li.tutor-dashboard-menu-divider-header,
li.tutor-dashboard-menu-item.tutor-dashboard-menu-my-courses,
li.tutor-dashboard-menu-item.tutor-dashboard-menu-announcements,
li.tutor-dashboard-menu-item.tutor-dashboard-menu-withdraw,
li.tutor-dashboard-menu-item.tutor-dashboard-menu-quiz-attempts {
    display: none !important;
}

/* Menü linklerini de gizle - ikinci grup */
a[href*="my-courses"],
a[href*="announcements"],
a[href*="withdraw"],
a[href*="quiz-attempts"] {
    display: none !important;
}

/* Dashboard menü container'ı içindeki spesifik linkler - ikinci grup */
.tutor-dashboard-left-menu a[href*="my-courses"],
.tutor-dashboard-left-menu a[href*="announcements"],
.tutor-dashboard-left-menu a[href*="withdraw"],
.tutor-dashboard-left-menu a[href*="quiz-attempts"] {
    display: none !important;
}

/*--------------------------------------------------------------
24. EĞİTMEN SEÇENEKLERİNİ GİZLEME
--------------------------------------------------------------*/

/**
 * KONTROL PANELİ VE SORU-CEVAP SAYFALARINDAKİ EĞİTMEN SEÇENEKLERİNİ GİZLE
 * Bu bölüm, dashboard ve soru-cevap sayfalarındaki eğitmen özel seçeneklerini gizler.
 * Eğitmen paneli butonu, eğitmen görünümü toggle'ı ve eğitmen özel alanları gizlenir.
 */

/* Eğitmen Paneli butonunu gizle - Dashboard header'da */
.tutor-btn[href*="wp-admin"]:contains("Eğitmen Paneli"),
a[href*="wp-admin"].tutor-btn:contains("Eğitmen Paneli"),
.tutor-header-right-side .tutor-btn[target="_blank"]:contains("Eğitmen"),
.tutor-header-right-side a.tutor-btn[href*="wp-admin"] {
    display: none !important;
}

/* Eğitmen Paneli butonunu gizle - daha spesifik seçici */
.tutor-dashboard-header .tutor-btn.tutor-btn-outline-primary[href*="wp-admin"],
.tutor-frontend-dashboard-header .tutor-btn.tutor-btn-outline-primary[href*="wp-admin"] {
    display: none !important;
}

/* Avatar dropdown'daki eğitmen rolü yazısını gizle */
.tutor-user-role:contains("Eğitmen"),
.tutor-avatar-dropdown-header .tutor-user-role {
    display: none !important;
}

/* Avatar dropdown'daki eğitmen rating'lerini gizle */
.tutor-dashboard-header-ratings,
.tutor-avatar-dropdown-stats .tutor-dashboard-header-ratings {
    display: none !important;
}

/* Soru-Cevap sayfasındaki eğitmen görünümü toggle'ını gizle */
.tutor-dashboard-qna-vew-as,
.tutor-form-toggle.tutor-dashboard-qna-vew-as {
    display: none !important;
}

/* Soru-Cevap sayfasındaki "Student/Instructor" toggle container'ını gizle */
.tutor-col-auto:has(.tutor-dashboard-qna-vew-as) {
    display: none !important;
}

/* My Courses sayfasındaki "Kurs Oluştur" butonunu gizle */
.tutor-create-new-course,
.tutor-btn.tutor-create-new-course,
a.tutor-btn.tutor-create-new-course {
    display: none !important;
}

/* Kurs düzenleme butonlarını gizle */
.tutor-my-course-edit,
.tutor-iconic-btn.tutor-my-course-edit,
a.tutor-iconic-btn.tutor-my-course-edit {
    display: none !important;
}

/* Kurs dropdown menülerini gizle (kebab menu) */
.tutor-dropdown-parent:has(.tutor-iconic-btn[action-tutor-dropdown]),
.tutor-iconic-btn[action-tutor-dropdown] {
    display: none !important;
}

/* Eğitmen özel menü öğelerini gizle */
.tutor-dashboard-menu-item[class*="instructor"],
.tutor-dashboard-menu-item[class*="teacher"] {
    display: none !important;
}

/* Settings sayfasındaki "Withdraw" sekmesini gizle */
.tutor-nav-item:has(a[href*="withdrawal"]),
.tutor-nav-link[href*="withdrawal"] {
    display: none !important;
}

/* Eğitmen durumu mesajlarını gizle */
.tutor-instructor-status,
.instructor-status-message,
.tutor-instructor-approval-message {
    display: none !important;
}

/* KONTROL PANELİNDEKİ EĞİTMEN KARTLARINI GİZLE */

/* Belirtilen selector'lara göre eğitmen kartlarını gizle */
#tutor-page-wrap .tutor-frontend-dashboard-maincontent .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div > div:nth-child(2) > div > div:nth-child(4),
#tutor-page-wrap .tutor-frontend-dashboard-maincontent .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div > div:nth-child(2) > div > div:nth-child(5),
#tutor-page-wrap .tutor-frontend-dashboard-maincontent .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 > div > div:nth-child(2) > div > div:nth-child(6) {
    display: none !important;
}

/* Alternatif seçiciler - eğitmen kartları için */
.tutor-dashboard-content-inner > div:nth-child(2) > div > div:nth-child(4),
.tutor-dashboard-content-inner > div:nth-child(2) > div > div:nth-child(5),
.tutor-dashboard-content-inner > div:nth-child(2) > div > div:nth-child(6) {
    display: none !important;
}

/* Dashboard ana içerik alanındaki eğitmen kartları */
.tutor-frontend-dashboard-maincontent div:nth-child(4),
.tutor-frontend-dashboard-maincontent div:nth-child(5),
.tutor-frontend-dashboard-maincontent div:nth-child(6) {
    display: none !important;
}

/* Eğitmen özel dashboard kartları - class tabanlı seçiciler */
.tutor-dashboard-card[class*="instructor"],
.tutor-dashboard-card[class*="teacher"],
.tutor-card[class*="instructor"],
.tutor-card[class*="teacher"] {
    display: none !important;
}

/* Eğitmen istatistik kartları */
.tutor-dashboard-stats-card[data-stat*="instructor"],
.tutor-dashboard-stats-card[data-stat*="teacher"],
.tutor-stats-card[data-stat*="instructor"] {
    display: none !important;
}